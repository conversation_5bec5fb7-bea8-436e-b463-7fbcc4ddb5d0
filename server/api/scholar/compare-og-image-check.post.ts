export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { researcher1Name, researcher2Name } = body

    if (!researcher1Name || !researcher2Name) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Both researcher1N<PERSON> and researcher2N<PERSON> are required'
      })
    }

    // 生成可能的文件名组合
    const possibleFileNames = [
      // 使用姓名的备选方案（与当前逻辑一致）
      `scholar-compare-${sanitizeName(researcher1Name)}-vs-${sanitizeName(researcher2Name)}-latest.png`,
      // 反向组合
      `scholar-compare-${sanitizeName(researcher2Name)}-vs-${sanitizeName(researcher1Name)}-latest.png`,
    ]

    // 检查S3上是否存在这些文件
    for (const fileName of possibleFileNames) {
      const ogImageUrl = `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${fileName}`
      
      try {
        // 使用HEAD请求检查文件是否存在
        const response = await fetch(ogImageUrl, { method: 'HEAD' })
        if (response.ok) {
          console.log(`Found existing Scholar compare OG image: ${ogImageUrl}`)
          return {
            success: true,
            ogImageUrl,
            fileName,
            found: true
          }
        }
      } catch (error) {
        // 继续检查下一个可能的文件名
        continue
      }
    }

    // 如果没有找到现有图片，返回默认的可预测URL
    const defaultFileName = possibleFileNames[0]
    const defaultOgImageUrl = `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${defaultFileName}`

    console.log(`No existing Scholar compare OG image found, using default: ${defaultOgImageUrl}`)
    
    return {
      success: true,
      ogImageUrl: defaultOgImageUrl,
      fileName: defaultFileName,
      found: false
    }

  } catch (error) {
    console.error('Error checking Scholar compare OG image:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to check Scholar compare OG image'
    })
  }
})

// 姓名清理函数（与前端保持一致）
function sanitizeName(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')  // 替换非字母数字为连字符
    .replace(/-+/g, '-')         // 合并多个连字符
    .replace(/^-|-$/g, '')       // 移除首尾连字符
}
